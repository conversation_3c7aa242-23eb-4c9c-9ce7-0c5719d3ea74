#!/usr/bin/env python3
"""
调试新闻处理脚本
"""

import pandas as pd
import sys
sys.path.append('.')
from create_news_training_samples import (
    parse_matched_symbols, 
    is_excluded_symbol, 
    convert_timestamp_ns_to_datetime,
    get_available_symbols,
    load_symbol_labeled_data
)

def debug_single_news_file(file_path, max_rows=5):
    """调试单个新闻文件的处理过程"""
    print(f"=== 调试文件: {file_path} ===")
    
    # 读取新闻文件
    try:
        news_df = pd.read_csv(file_path)
        print(f"✅ 成功读取文件，共 {len(news_df)} 条新闻")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 获取可用symbols
    available_symbols = get_available_symbols()
    print(f"📋 可用symbols数量: {len(available_symbols)}")
    print(f"🔗 前10个symbols: {available_symbols[:10]}")
    
    # 处理前几条新闻
    for i, (_, news_row) in enumerate(news_df.head(max_rows).iterrows()):
        print(f"\n--- 处理第 {i+1} 条新闻 ---")
        
        # 提取基本信息
        title = news_row.get('title', '')
        matched_symbols = news_row.get('matched_symbols', '')
        timestamp_ns = news_row.get('timestamp_ns', '')
        
        print(f"📰 标题: {title[:50]}...")
        print(f"🔗 原始matched_symbols: {matched_symbols}")
        print(f"⏰ timestamp_ns: {timestamp_ns}")
        
        # 解析symbols
        symbols = parse_matched_symbols(matched_symbols)
        print(f"🎯 解析后的symbols: {symbols}")
        
        if not symbols:
            print("⚠️  没有解析到symbols，跳过")
            continue
        
        # 检查时间戳转换
        utc_time = convert_timestamp_ns_to_datetime(timestamp_ns)
        print(f"🕐 转换后的UTC时间: {utc_time}")
        
        if utc_time is None:
            print("⚠️  时间戳转换失败，跳过")
            continue
        
        # 检查每个symbol
        for symbol in symbols:
            print(f"\n  🔍 检查symbol: {symbol}")
            
            # 检查是否被排除
            if is_excluded_symbol(symbol):
                print(f"    ❌ 被排除规则过滤")
                continue
            
            # 检查是否在可用列表中
            if symbol not in available_symbols:
                print(f"    ❌ 不在可用symbols列表中")
                continue
            
            # 尝试加载数据
            labeled_df = load_symbol_labeled_data(symbol)
            if labeled_df is None:
                print(f"    ❌ 无法加载标签数据")
                continue
            
            print(f"    ✅ 成功加载标签数据，共 {len(labeled_df)} 行")
            print(f"    📊 时间范围: {labeled_df['timestamp'].min()} 到 {labeled_df['timestamp'].max()}")
            
            # 检查是否有匹配的时间点
            future_data = labeled_df[labeled_df['timestamp'] >= utc_time]
            print(f"    🎯 找到 {len(future_data)} 个未来时间点")
            
            if len(future_data) > 0:
                print(f"    ✅ 可以生成训练样本！")
            else:
                print(f"    ⚠️  没有找到未来时间点")

def main():
    """主函数"""
    # 调试第一个新闻文件
    file_path = "../filter_news/panews_flash_20250101_filtered.csv"
    debug_single_news_file(file_path, max_rows=3)

if __name__ == "__main__":
    main()
