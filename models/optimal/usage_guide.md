
# 最优模型使用指南

## 模型文件
- RandomForest模型: models/optimal/rf_model.pkl
- TF-IDF向量化器: models/optimal/tfidf_vectorizer.pkl
- 模型配置: models/optimal/model_config.json
- 路径配置: models/optimal/model_paths.json

## 最优配置
- 阈值: [-0.02, -0.005, 0.005, 0.02]
- 准确率: 0.6911
- 特征维度: 306
- 交易可行性: 5.0倍手续费

## 在策略中使用
在 NewsPnlTradingStrategyConfig 中设置:
- tokenizer_path: "models/baseline_2k.model"
- tfidf_path: "models/optimal/tfidf_vectorizer.pkl"
- model_path: "models/optimal/rf_model.pkl"

## 模型加载验证
可以使用以下代码验证模型加载:
```python
import pickle
with open('models/optimal/rf_model.pkl', 'rb') as f:
    model = pickle.load(f)
print("模型加载成功:", type(model))
```
