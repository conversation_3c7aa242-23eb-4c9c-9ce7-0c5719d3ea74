#!/usr/bin/env python3
"""
测试优化后的新闻训练样本生成脚本
只处理前5个文件来验证功能
"""

import sys
sys.path.append('.')
from create_news_training_samples import (
    get_available_symbols,
    process_news_file,
    save_samples_to_csv
)
import glob

def main():
    """主函数 - 测试处理前5个文件"""
    print("=== 测试优化后的新闻训练样本生成器 ===")
    print("🚀 使用已处理的timestamp_ns字段，提升处理效率")

    # 获取可用的symbols
    available_symbols = get_available_symbols()
    print(f"📋 可用symbols: {len(available_symbols)}")
    print(f"🔗 前10个symbols: {', '.join(available_symbols[:10])}")

    # 处理前5个新闻文件
    file_pattern = "../filter_news/panews_flash_*_filtered.csv"
    news_files = glob.glob(file_pattern)
    news_files.sort()  # 按文件名排序

    if not news_files:
        print(f"❌ 没有找到匹配的文件: {file_pattern}")
        return

    print(f"📁 找到 {len(news_files)} 个新闻文件，处理前5个")

    all_samples = []
    
    # 只处理前5个文件
    for i, news_file in enumerate(news_files[:5], 1):
        print(f"\n[{i}/5] 处理文件: {news_file}")

        samples = process_news_file(news_file, available_symbols)
        if samples:
            all_samples.extend(samples)
            print(f"✅ 生成 {len(samples)} 个样本")
        else:
            print("⚠️  没有生成样本")

    if all_samples:
        # 保存样本
        output_file = "test_news_training_samples.csv"
        save_samples_to_csv(all_samples, output_file)

        print(f"\n🎉 测试完成:")
        print(f"📁 处理文件数: 5")
        print(f"📊 总样本数: {len(all_samples)}")
        print(f"💾 输出文件: {output_file}")
    else:
        print("❌ 没有生成任何样本")

if __name__ == "__main__":
    main()
