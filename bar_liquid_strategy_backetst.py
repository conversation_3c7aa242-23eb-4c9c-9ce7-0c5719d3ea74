#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

from decimal import Decimal
import os
import pandas as pd
from datetime import datetime, timedelta

from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.config import BacktestRunConfig
from nautilus_trader.config import BacktestVenueConfig
from nautilus_trader.config import BacktestDataConfig
from nautilus_trader.config import RiskEngineConfig
from nautilus_trader.config import LoggingConfig
from strategies.liquid_strategy import OrderLiquidStrategy
from strategies.liquid_strategy import OrderLiquidConfig
from nautilus_trader.model.enums import OrderType
from nautilus_trader.model.identifiers import InstrumentId, Venue, ClientId
from nautilus_trader.model.currencies import USD, USDT
from nautilus_trader.model.objects import Money
from nautilus_trader.persistence.catalog.parquet import ParquetDataCatalog
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder
from nautilus_trader.adapters.binance.common.types import BinanceBar
from nautilus_trader.model.data import BarType
from nautilus_trader.model.data import BarSpecification
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.config import ImportableStrategyConfig
from nautilus_trader.config import LoggingConfig
from nautilus_trader.backtest.engine import BacktestEngineConfig
from nautilus_trader.serialization.arrow.serializer import register_arrow
from nautilus_trader.backtest.models import FillModel
# Register the custom data type for catalog operations
register_arrow(
    BinanceFuturesLiquidationOrder,
    BinanceFuturesLiquidationOrder.schema(),
    BinanceFuturesLiquidationOrder.to_catalog,
    BinanceFuturesLiquidationOrder.from_catalog,
)

def run_backtest() -> None:
    """
    Run a backtest for the OrderLiquidStrategy using BacktestNode.
    """
    # Load data from catalog first
    catalog_path = "../catalog"
    catalog = ParquetDataCatalog(path=catalog_path)

    # Setup test instruments
    instrument_id = "WLDUSDT-PERP.BINANCE"
    instrument = catalog.instruments(instrument_ids=[instrument_id], as_nautilus=True)[0]
    bar_type = f"{instrument_id}-1-MINUTE-LAST-EXTERNAL"
    internal_bar_type = f"{instrument_id}-5-MINUTE-LAST-EXTERNAL"

    fill_model = FillModel(
        prob_fill_on_limit=0.7,    # Chance a limit order fills when price matches (applied to bars/trades/quotes + L1/L2/L3 orderbook)
        prob_fill_on_stop=0.95,    # [DEPRECATED] Will be removed in a future version, use `prob_slippage` instead
        prob_slippage=0.5,         # Chance of 1-tick slippage (applied to bars/trades/quotes + L1 orderbook only)
        random_seed=None,          # Optional: Set for reproducible results
    )

    data_configs = [
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=BinanceFuturesLiquidationOrder.fully_qualified_name(),
            instrument_id = InstrumentId.from_str(instrument_id),
            start_time = datetime(2025, 1, 22),
            end_time = datetime(2025, 2, 6),
            client_id="BINANCE",
            metadata={"instrument_id": instrument_id},
        ),
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=BinanceBar,
            instrument_id = InstrumentId.from_str(instrument_id),
            start_time = datetime(2025, 1, 22),
            end_time = datetime(2025, 2, 6),
            client_id="BINANCE",
        ),
    ]
    venues_configs = [
        BacktestVenueConfig(
            name="BINANCE",
            oms_type="NETTING",
            account_type="MARGIN",
            base_currency="USDT",
            starting_balances=["100000 USDT"],
        )
    ]
    strategies = [
        ImportableStrategyConfig(
            strategy_path="strategies.ml_liquid_strategy2:OrderLiquidStrategy",
            config_path="strategies.ml_liquid_strategy2:OrderLiquidConfig",
            config=dict(
                        instrument_id=instrument_id,
                        bar_type = bar_type,
                        internal_bar_type = internal_bar_type,
                        request_bar_days = 1,
                        # Position executor params
                        atr_multiplier=7.0,
                        stop_loss=9,  # 2% stop loss
                        speed=0.4,
                        take_profit=0.15,  # 4% take profit
                        order_time_limit=30,  # 30 second order timeout
                        position_time_limit=None,#5 minutes
                        trailing_stop=0.1,  # 1% trailing stop
                        trail_stop_loss_pct=0.05,  # 0.5% trail stop loss
                        trail_stop_order_type=OrderType.LIMIT,
                        take_profit_order_type=OrderType.LIMIT,
                        stop_loss_order_type=OrderType.LIMIT,
                        time_limit_order_type=OrderType.LIMIT,
                        tp_n_tickers=1,
                        # ML model params
                        model_prefix="WLDUSDT",
                        model_save_dir="models",
                        lgb_model_num=5,
                        max_trade_usd=1000.0,
                        min_notional=100.0,
                        trade_features_path="WLDUSDT_trade_features.pkl",
                        liquidation_features_path="WLDUSDT_liquid_features.pkl",
                        liquidation_data_path="../utils/data/record",
            
            )
        ),
    ]
    base_config = [
        BacktestRunConfig(
        engine=BacktestEngineConfig(
            strategies=strategies,
            fill_model=fill_model,
            logging=LoggingConfig(log_level="INFO"),
            ),
        venues=venues_configs,
        data=data_configs,
        )
    ]

    # Create backtest node
    node = BacktestNode(base_config)


    # Run backtest
    node.run()
    # Optionally view reports
    with pd.option_context(
        "display.max_rows",
        100,
        "display.max_columns",
        None,
        "display.width",
        300,
    ):
        print(node.get_engine(base_config[0].id).trader.generate_order_fills_report())
        print(node.get_engine(base_config[0].id).trader.generate_positions_report())


if __name__ == "__main__":
    run_backtest()
