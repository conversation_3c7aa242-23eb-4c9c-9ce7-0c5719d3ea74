#!/usr/bin/env python3
"""
新闻预测策略回测脚本
直接使用 news_prediction_strategy.py 中的 NewsPnlTradingStrategy
加载 1000PEPE 和 BTC 的 5分钟 K线数据和新闻数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timezone, timedelta
from decimal import Decimal

# Nautilus Trader imports
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.config import BacktestRunConfig, BacktestEngineConfig, BacktestVenueConfig, BacktestDataConfig
from nautilus_trader.config import LoggingConfig, ImportableStrategyConfig
from nautilus_trader.model.currencies import USDT
from nautilus_trader.model.enums import AccountType, OmsType
from nautilus_trader.model.identifiers import InstrumentId, Venue
from nautilus_trader.model.objects import Money
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.serialization.arrow.serializer import register_arrow
from nautilus_trader.backtest.models import FillModel
from nautilus_trader.model.data import Bar
# 策略和数据类型导入 - 使用绝对路径
import sys
sys.path.append('/root/news_train')
sys.path.append('/root/news_train/process/strategy')

from news_prediction_strategy import NewsPnlTradingStrategy, NewsPnlTradingStrategyConfig
from news_data_type import NewsData

# 注册自定义数据类型
register_arrow(
    NewsData,
    NewsData.schema(),
    NewsData.to_catalog,
    NewsData.from_catalog,
)


def create_start_news() -> NewsData:
    """创建初始新闻数据"""
    from nautilus_trader.core.datetime import dt_to_unix_nanos

    now = datetime(2025, 1, 15, 10, 0, 0, tzinfo=timezone.utc)
    ts_nanos = dt_to_unix_nanos(now)

    return NewsData(
        instrument_id=InstrumentId.from_str("1000PEPEUSDT-PERP.BINANCE"),
        title="PEPE价格突破关键阻力位，市场情绪乐观",
        content="PEPE代币今日突破重要技术阻力位，交易量大幅增加，市场分析师认为这可能是新一轮上涨的开始。技术指标显示强劲的买入信号。",
        time=now.strftime("%H:%M:%S"),
        date=now.strftime("%Y-%m-%d"),
        url="https://example.com/news/pepe-1",
        is_featured=True,
        scraped_at=now.strftime("%Y-%m-%dT%H:%M:%SZ"),
        ts_event=ts_nanos,
        ts_init=ts_nanos
    )


def run_news_strategy_backtest() -> None:
    """
    运行新闻预测策略回测
    """
    print("🚀 开始新闻预测策略回测...")
    print("📈 测试标的: 1000PEPEUSDT, BTCUSDT")
    print("🤖 使用最优模型: 准确率 69.11%")
    
    # 路径配置 - 使用绝对路径
    catalog_path = "/root/nautilus_trader-develop/examples/catalog"
    
    # 交易工具配置
    pepe_instrument_id = "1000PEPEUSDT-PERP.BINANCE"
    btc_instrument_id = "BTCUSDT-PERP.BINANCE"
    
    # 使用最优模型路径 - 绝对路径
    tokenizer_path = "/root/news_train/models/baseline_2k.model"
    tfidf_path = "/root/news_train/models/optimal/tfidf_vectorizer.pkl"
    model_path = "/root/news_train/models/optimal/rf_model.pkl"
    
    # 检查必要文件
    required_files = [tokenizer_path, tfidf_path, model_path]
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 创建填充模型
    fill_model = FillModel(
        prob_fill_on_limit=0.7,    # 限价单成交概率
        prob_fill_on_stop=0.95,    # 止损单成交概率
        prob_slippage=0.5,         # 滑点概率
        random_seed=42,            # 随机种子，确保结果可重现
    )
    
    # 数据配置 - 加载新闻数据、PEPE和BTC的K线数据
    data_configs = [
        # 新闻数据配置
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=NewsData.fully_qualified_name(),
            client_id="PANEWS",
            start_time=datetime(2025, 1, 1),
            end_time=datetime(2025, 1, 31),
        ),
        # 1000PEPE K线数据配置
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=Bar.fully_qualified_name(),
            instrument_id=InstrumentId.from_str(pepe_instrument_id),
            start_time=datetime(2025, 1, 1),
            end_time=datetime(2025, 1, 31),
            client_id="BINANCE",
        ),
        # BTC K线数据配置
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=Bar.fully_qualified_name(),
            instrument_id=InstrumentId.from_str(btc_instrument_id),
            start_time=datetime(2025, 1, 15),
            end_time=datetime(2025, 1, 31),
            client_id="BINANCE",
        ),
    ]
    
    # 场所配置
    venues_configs = [
        BacktestVenueConfig(
            name="BINANCE",
            oms_type="NETTING",
            account_type="MARGIN",
            base_currency="USDT",
            starting_balances=["100000 USDT"],
        )
    ]
    
    # 创建初始新闻数据 - 从外部传入策略
    from nautilus_trader.core.datetime import dt_to_unix_nanos

    news_time = datetime(2025, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
    ts_nanos = dt_to_unix_nanos(news_time)

    # 创建 NewsData 对象
    start_news = NewsData(
        instrument_id=InstrumentId.from_str(pepe_instrument_id),
        title="PEPE价格突破关键阻力位，市场情绪乐观",
        content="PEPE代币今日突破重要技术阻力位，交易量大幅增加，市场分析师认为这可能是新一轮上涨的开始。技术指标显示强劲的买入信号。",
        time="10:00:00",
        date="2025-01-01",
        url="https://example.com/news/pepe-1",
        is_featured=True,
        scraped_at="2025-01-01T10:00:00Z",
        ts_event=ts_nanos,
        ts_init=ts_nanos
    )

    # 转换为字典格式用于配置
    start_news_dict = {
        "instrument_id": pepe_instrument_id,
        "title": start_news.title,
        "content": start_news.content,
        "time": start_news.time,
        "date": start_news.date,
        "url": start_news.url,
        "is_featured": start_news.is_featured,
        "scraped_at": start_news.scraped_at,
        "ts_event": start_news.ts_event,
        "ts_init": start_news.ts_init
    }

    # 策略配置 - 直接使用原始策略，使用绝对路径
    strategies = [
        ImportableStrategyConfig(
            strategy_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategy",
            config_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategyConfig",
            config={
                # 使用最优模型路径
                "tokenizer_path": tokenizer_path,
                "tfidf_path": tfidf_path,
                "model_path": model_path,

                # 交易工具
                "instrument_str": pepe_instrument_id,
                "btc_instrument_str": btc_instrument_id,
                "bar_str": f"{pepe_instrument_id}-5-MINUTE-LAST-EXTERNAL",
                "btc_bar_str": f"{btc_instrument_id}-5-MINUTE-LAST-EXTERNAL",

                # 风险管理参数（基于最优模型调优）
                "stop_loss_pct": 0.02,                      # 止损 2%
                "low_trail_stop_loss_pct": 0.5,             # 低位追踪止损 50%
                "trail_stop_loss_pct": 0.3,                 # 追踪止损 30%
                "higher_trail_stop_loss_pct": 0.2,          # 高位追踪止损 20%
                "low_trail_profit_threshold": 0.01,         # 低位追踪盈利阈值 1%
                "first_trail_profit_threshold": 0.02,       # 第一追踪盈利阈值 2%
                "second_trail_profit_threshold": 0.03,      # 第二追踪盈利阈值 3%

                # 交易金额
                "max_trade_usd": 1000.0,                    # 最大交易金额
                "min_trade_usd": 100.0,                     # 最小交易金额

                # 策略ID
                "strategy_id": "OptimalNewsPnlStrategy-Backtest",

                # 初始新闻数据 - 字典格式
                "start_news": start_news_dict
            }
        ),
    ]
    
    # 回测配置
    backtest_configs = [
        BacktestRunConfig(
            engine=BacktestEngineConfig(
                strategies=strategies,
                logging=LoggingConfig(log_level="INFO"),
            ),
            venues=venues_configs,
            data=data_configs,
        )
    ]
    
    print("📁 创建回测节点...")
    # 创建回测节点
    node = BacktestNode(backtest_configs)
    
    try:
        print("⏳ 正在运行回测...")
        # 运行回测
        node.run()
        
        print("✅ 回测完成!")
        
        # 获取回测结果
        engine = node.get_engine(backtest_configs[0].id)
        
        if engine:
            print("\n📊 回测结果分析:")
            
            # 生成报告
            with pd.option_context(
                "display.max_rows", 100,
                "display.max_columns", None,
                "display.width", 300,
            ):
                print("\n📋 订单成交报告:")
                print(engine.trader.generate_order_fills_report())
                
                print("\n📊 持仓报告:")
                print(engine.trader.generate_positions_report())
                
                # 账户信息
                accounts = engine.cache.accounts()
                if accounts:
                    account = accounts[0]  # 获取第一个账户
                    print(f"\n💰 最终账户余额: {account.balance_total()}")
                else:
                    print("\n💰 未找到账户信息")
                
                # 统计信息
                orders = engine.cache.orders()
                positions = engine.cache.positions()
                
                print(f"📋 总订单数: {len(orders)}")
                print(f"📊 总仓位数: {len(positions)}")
                
                if orders:
                    filled_orders = [o for o in orders if o.is_filled]
                    print(f"✅ 成交订单数: {len(filled_orders)}")
                
                if positions:
                    closed_positions = [p for p in positions if p.is_closed]
                    print(f"🔒 已关闭仓位: {len(closed_positions)}")
                    
                    if closed_positions:
                        total_pnl = sum(p.realized_pnl.as_double() for p in closed_positions)
                        print(f"💵 已实现PnL: {total_pnl:.2f} USDT")
                        
                        profitable_positions = [p for p in closed_positions if p.realized_pnl.as_double() > 0]
                        win_rate = len(profitable_positions) / len(closed_positions) * 100
                        print(f"🎯 胜率: {win_rate:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        node.dispose()


def main():
    """主函数"""
    print("=" * 80)
    print("🎯 新闻预测策略回测系统")
    print("=" * 80)
    print("📊 使用最优模型配置:")
    print("   - 阈值: [-0.02, -0.005, 0.005, 0.02]")
    print("   - 准确率: 69.11%")
    print("   - 特征维度: 306")
    print("   - 交易可行性: 5倍手续费")
    print("=" * 80)

    # 运行回测
    success = run_news_strategy_backtest()

    if success:
        print("\n🎉 回测成功完成!")
        print("💡 回测使用了真实的K线数据和最优训练模型")
        print("💡 策略基于新闻事件进行交易决策")
    else:
        print("\n💥 回测失败!")
        print("💡 请检查数据文件和模型文件是否存在")

    print("=" * 80)


if __name__ == "__main__":
    main()
