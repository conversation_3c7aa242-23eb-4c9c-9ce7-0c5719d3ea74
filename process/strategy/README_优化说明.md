# write_news_to_catalog.py 优化说明

## 概述

已成功优化 `write_news_to_catalog.py` 文件，使其能够处理 `news_training_samples_complete_all.csv` 格式的数据，并将新闻数据保存到 Nautilus Trader 的 catalog 中。

## 主要优化内容

### 1. 支持新的CSV格式
- **自动格式检测**：程序会自动检测CSV文件格式
- **新列名支持**：
  - `news_title` → 新闻标题
  - `news_content` → 新闻内容  
  - `news_time_utc8` → UTC+8时间
  - `news_timestamp_ns` → 纳秒时间戳
  - `symbol` → 交易对符号
- **向后兼容**：仍支持原有的 `matched_symbols` 格式

### 2. Instrument ID 处理（永续合约格式）
- **格式验证**：添加 `validate_instrument_id()` 函数
- **永续合约转换**：自动将现货交易对转换为永续合约格式
- **支持格式**：
  - 标准格式：`BTCUSDT` → `BTCUSDT-PERP.BINANCE`
  - 前缀格式：`1000PEPEUSDT` → `1000PEPEUSDT-PERP.BINANCE`
  - 已有PERP：`BTCUSDT-PERP` → `BTCUSDT-PERP.BINANCE`
  - 其他格式：`ETHUSDT` → `ETHUSDT-PERP.BINANCE`

### 3. 时间戳处理
- **优先级**：优先使用 `news_timestamp_ns` 字段
- **Fallback机制**：如果时间戳无效，使用时间解析
- **验证**：验证时间戳在合理范围内（2020-2030年）

## 使用方法

### 基本用法

```bash
# 处理默认的news_training_samples_complete_all.csv文件
python write_news_to_catalog.py --catalog-path /path/to/catalog

# 处理指定的CSV文件
python write_news_to_catalog.py \
    --input-file process/news_training_samples_complete_all.csv \
    --catalog-path /path/to/catalog

# 详细日志输出
python write_news_to_catalog.py \
    --input-file process/news_training_samples_complete_all.csv \
    --catalog-path /path/to/catalog \
    --log-level DEBUG
```

### 高级用法

```bash
# 处理整个目录
python write_news_to_catalog.py \
    --input-dir process/filter_news/ \
    --catalog-path /path/to/catalog

# 指定日期范围
python write_news_to_catalog.py \
    --input-dir process/filter_news/ \
    --catalog-path /path/to/catalog \
    --start-date 2025-01-01 \
    --end-date 2025-01-31
```

## 输出格式

处理后的数据将保存在指定的catalog目录中，所有交易对都转换为永续合约格式：
```
catalog/
└── data/
    └── custom_news_data/
        ├── BTCUSDT-PERP.BINANCE/
        │   └── 2025-01-01T12-22-00-000000000Z_2025-01-01T12-22-00-000000000Z.parquet
        ├── 1000PEPEUSDT-PERP.BINANCE/
        │   └── 2025-01-01T12-25-00-000000000Z_2025-01-01T12-25-00-000000000Z.parquet
        ├── VIRTUALUSDT-PERP.BINANCE/
        │   └── 2025-01-02T15-09-00-000000000Z_2025-01-02T15-09-00-000000000Z.parquet
        └── ...
```

## 验证和测试

### 验证instrument_id格式
程序会自动验证以下格式：
- ✓ `BTCUSDT` → 有效（包含USDT）
- ✓ `1000PEPEUSDT` → 有效（移除1000前缀后包含USDT）
- ✓ `ETHBTC` → 有效（包含BTC）
- ✗ `INVALID` → 无效（不包含有效的基础货币）

### 支持的基础货币
- USDT
- BTC  
- ETH
- BNB
- BUSD

## 性能优化

- **批处理**：使用1000条记录的批次处理
- **内存优化**：指定数据类型减少内存使用
- **进度显示**：使用tqdm显示处理进度
- **错误处理**：跳过无效记录，继续处理其他数据

## 注意事项

1. **Venue要求**：Nautilus Trader要求instrument_id包含venue信息，程序会自动添加`.BINANCE`
2. **时间戳格式**：使用纳秒时间戳，确保与Nautilus Trader兼容
3. **数据验证**：程序会验证必需字段，跳过无效记录
4. **日志级别**：建议使用INFO级别查看处理进度，DEBUG级别查看详细信息

## 错误排查

如果遇到问题，请：
1. 检查CSV文件格式是否正确
2. 确认catalog目录有写入权限
3. 查看日志输出了解具体错误
4. 使用DEBUG日志级别获取更多信息
