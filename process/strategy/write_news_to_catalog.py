#!/usr/bin/env python
"""
将新闻数据写入nautilus_trader的catalog。
优化版本：使用filter_news目录中的已过滤数据，支持timestamp_ns字段。

Usage examples:
    # 处理默认filter_news目录中的所有过滤文件
    python write_news_to_catalog.py --catalog-path /root/nautilus_trader-develop/examples/catalog

    # 处理单个文件
    python write_news_to_catalog.py --input-file process/filter_news/panews_flash_20250101_filtered.csv --catalog-path /root/nautilus_trader-develop/examples/catalog

    # 处理整个目录
    python write_news_to_catalog.py --input-dir process/filter_news/ --catalog-path /root/nautilus_trader-develop/examples/catalog

    # 指定特定日期范围
    python write_news_to_catalog.py --input-dir process/filter_news/ --catalog-path /root/nautilus_trader-develop/examples/catalog --start-date 2025-01-01 --end-date 2025-01-31

优化特性：
- 默认使用process/filter_news目录
- 使用timestamp_ns字段作为ts_event和ts_init
- 批处理提高性能
- 优化内存使用
- 支持matched_symbols字段
"""

import logging
import argparse
import time
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
import pytz
from tqdm import tqdm


from nautilus_trader.core import Data
from nautilus_trader.model.identifiers import InstrumentId, Symbol, Venue
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.serialization.base import register_serializable_type
from nautilus_trader.serialization.arrow.serializer import register_arrow
import pyarrow as pa



# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from news_data_type import NewsData

def validate_timestamp_ns(timestamp_ns: int) -> bool:
    """
    验证timestamp_ns是否为合理的纳秒时间戳。

    Args:
        timestamp_ns: 纳秒时间戳

    Returns:
        True如果时间戳合理，False否则
    """
    try:
        # 检查是否为正数
        if timestamp_ns <= 0:
            return False

        # 转换为秒时间戳检查范围
        timestamp_s = timestamp_ns / 1_000_000_000

        # 检查是否在合理范围内 (2020-2030年)
        min_timestamp = datetime(2020, 1, 1, tzinfo=timezone.utc).timestamp()
        max_timestamp = datetime(2030, 12, 31, tzinfo=timezone.utc).timestamp()

        return min_timestamp <= timestamp_s <= max_timestamp

    except Exception:
        return False

def parse_time_to_utc(time_str: str, date_str: str) -> int:
    """
    将UTC+8时间转换为UTC+0的纳秒时间戳。

    Args:
        time_str: 时间字符串，如 "23:45"
        date_str: 日期字符串，如 "1月1日"

    Returns:
        UTC纳秒时间戳
    """
    try:
        # 解析日期字符串 "1月1日" -> "01-01"
        import re
        date_match = re.match(r'(\d+)月(\d+)日', date_str)
        if date_match:
            month = int(date_match.group(1))
            day = int(date_match.group(2))
            # 假设是2025年
            year = 2025
        else:
            # 如果解析失败，使用默认值
            year, month, day = 2025, 1, 1

        # 解析时间字符串 "23:45" -> 23, 45
        time_parts = time_str.split(':')
        hour = int(time_parts[0])
        minute = int(time_parts[1]) if len(time_parts) > 1 else 0

        # 创建UTC+8时间
        utc8_tz = pytz.timezone('Asia/Shanghai')
        dt_utc8 = utc8_tz.localize(datetime(year, month, day, hour, minute))

        # 转换为UTC
        dt_utc = dt_utc8.astimezone(pytz.UTC)

        # 转换为纳秒时间戳
        timestamp_ns = int(dt_utc.timestamp() * 1_000_000_000)

        return timestamp_ns

    except Exception as e:
        logger.warning(f"时间解析失败 {time_str} {date_str}: {e}")
        # 返回当前时间的纳秒时间戳作为fallback
        return int(datetime.now(timezone.utc).timestamp() * 1_000_000_000)

def split_news_by_symbols(row: pd.Series) -> List[Dict[str, Any]]:
    """
    根据matched_symbols字段拆分新闻。
    如果有多个symbols，将新闻拆分为多条记录。
    使用已有的timestamp_ns作为ts_event和ts_init。

    Args:
        row: pandas Series，代表一条新闻记录

    Returns:
        拆分后的新闻记录列表
    """
    news_records = []

    # 解析matched_symbols字段
    matched_symbols = str(row['matched_symbols']).strip()

    if not matched_symbols or matched_symbols == 'nan':
        logger.warning(f"新闻没有相关symbols，跳过: {row['title'][:50]}...")
        return news_records

    # 拆分symbols
    symbols = []
    if ',' in matched_symbols:
        # 多个symbols，用逗号分隔
        symbols = [s.strip() for s in matched_symbols.split(',')]
    else:
        # 单个symbol
        symbols = [matched_symbols]

    # 获取时间戳
    try:
        # 使用已有的timestamp_ns字段
        timestamp_ns = int(row['timestamp_ns'])

        # 验证时间戳是否合理
        if not validate_timestamp_ns(timestamp_ns):
            logger.warning(f"timestamp_ns值不合理: {timestamp_ns}, 使用时间解析fallback")
            raise ValueError("Invalid timestamp_ns")

        ts_event = timestamp_ns
        ts_init = timestamp_ns

    except (ValueError, KeyError) as e:
        logger.warning(f"无法解析timestamp_ns: {e}, 使用时间解析fallback")
        # 如果timestamp_ns不可用，使用原来的时间解析方法
        ts_event = parse_time_to_utc(row['time'], row['date'])
        ts_init = ts_event

    # 为每个symbol创建一条新闻记录
    for symbol in symbols:
        if not symbol:
            continue

        instrument_id = symbol 
        print(instrument_id)
    

        news_record = {
            'instrument_id': instrument_id,
            'title': row['title'],
            'content': row['content'],
            'time': row['time'],
            'date': row['date'],
            'url': row['url'],
            'is_featured': bool(row['is_featured']),
            'scraped_at': row['scraped_at'],
            'ts_event': ts_event,
            'ts_init': ts_init,
        }

        news_records.append(news_record)

    return news_records

def check_symbol_in_catalog(instrument_id: InstrumentId, catalog: ParquetDataCatalog) -> bool:
    """
    检查instrument_id是否在catalog中存在对应的instrument。

    Args:
        instrument_id: 要检查的instrument_id
        catalog: ParquetDataCatalog实例

    Returns:
        True如果instrument可交易，False否则
    """


    try:
        # 尝试查询catalog中的instruments
        instruments = catalog.instruments()

        # 检查instrument_id是否存在
        for instrument in instruments:
            if str(instrument.id) == str(instrument_id):
                logger.debug(f"找到匹配的instrument: {instrument.id}")
                return True

        # 如果没找到，记录警告但不跳过（因为catalog可能为空）
        logger.debug(f"Instrument {instrument_id} 不在catalog中，但继续处理")
        return True  # 暂时返回True，允许所有instrument

    except Exception as e:
        logger.warning(f"检查instrument {instrument_id} 时出错: {e}")
        return True  # 出错时默认允许

def process_csv_file(
    file_path: Path,
    catalog: ParquetDataCatalog,
) -> int:
    """
    处理单个CSV文件，转换为NewsData对象并写入catalog。

    Args:
        file_path: CSV文件路径
        catalog: ParquetDataCatalog实例

    Returns:
        处理的记录数
    """
    logger.info(f"处理文件: {file_path}")
    start_time = time.time()

    try:
        # 读取CSV文件，优化内存使用
        df = pd.read_csv(file_path, dtype={
            'title': 'string',
            'content': 'string',
            'time': 'string',
            'date': 'string',
            'url': 'string',
            'is_featured': 'bool',
            'scraped_at': 'string',
            'matched_symbols': 'string',
            'timestamp_ns': 'int64'
        })
        logger.info(f"从 {file_path.name} 读取了 {len(df)} 条记录")

        # 验证必需的列
        required_columns = ['title', 'content', 'time', 'date', 'url', 'is_featured', 'scraped_at',
                           'matched_symbols', 'timestamp_ns']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"文件 {file_path.name} 缺少必需的列: {missing_columns}")
            return 0

        # 处理每一行，拆分symbols
        all_news_data = []
        skipped_count = 0
        batch_size = 1000  # 批处理大小

        # 使用批处理提高性能
        for batch_start in range(0, len(df), batch_size):
            batch_end = min(batch_start + batch_size, len(df))
            batch_df = df.iloc[batch_start:batch_end]

            batch_news_data = []

            for _, row in tqdm(batch_df.iterrows(),
                             total=len(batch_df),
                             desc=f"处理新闻批次 {batch_start//batch_size + 1}",
                             leave=False):
                news_records = split_news_by_symbols(row)

                for news_record in news_records:
                    instrument_id = news_record['instrument_id']

                    # 检查instrument是否可交易
                    if not check_symbol_in_catalog(instrument_id, catalog):
                        logger.debug(f"跳过不可交易的instrument: {instrument_id}")
                        skipped_count += 1
                        continue

                    # 创建NewsData对象
                    try:
                        news_data = NewsData(**news_record)
                        batch_news_data.append(news_data)
                    except Exception as e:
                        logger.warning(f"创建NewsData失败: {e}, 记录: {news_record}")
                        continue

            all_news_data.extend(batch_news_data)

            # 每处理一个批次就记录进度
            if batch_news_data:
                logger.debug(f"批次 {batch_start//batch_size + 1} 处理了 {len(batch_news_data)} 条有效记录")

        if skipped_count > 0:
            logger.info(f"跳过了 {skipped_count} 条不可交易symbol的记录")

        if not all_news_data:
            logger.warning(f"文件 {file_path.name} 没有有效的新闻数据")
            return 0

        # 按ts_init排序数据（nautilus要求单调递增）
        all_news_data.sort(key=lambda x: x.ts_init)
        logger.debug(f"按ts_init排序了 {len(all_news_data)} 条新闻数据")

        # 写入catalog

        file_date_str = file_path.stem.split('_')[-1]  # 从文件名提取日期
        logger.info(f"写入 {len(all_news_data)} 条新闻到catalog (文件日期: {file_date_str})")

        catalog.write_data(all_news_data)

        elapsed_time = time.time() - start_time
        logger.info(f"成功处理 {len(all_news_data)} 条记录，用时 {elapsed_time:.2f} 秒 ({len(all_news_data)/elapsed_time:.2f} 记录/秒)")

        return len(all_news_data)

    except Exception as e:
        logger.error(f"处理文件 {file_path} 失败: {str(e)}", exc_info=True)
        return 0

def get_file_list(
    input_dir: Optional[Path] = None,
    input_files: Optional[List[Path]] = None,
    file_pattern: str = "*.csv",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    sort_files: bool = True,
) -> List[Path]:
    """
    获取要处理的文件列表。

    Args:
        input_dir: 输入目录路径
        input_files: 输入文件路径列表
        file_pattern: 文件匹配模式
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        sort_files: 是否按字母顺序排序

    Returns:
        文件路径列表
    """
    files = []

    if input_dir:
        # 从目录获取文件
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return []

        files = list(input_dir.glob(file_pattern))
        if not files:
            logger.warning(f"在目录 {input_dir} 中没有找到匹配 '{file_pattern}' 的文件")

    elif input_files:
        # 使用提供的文件列表
        files = [Path(f) for f in input_files if Path(f).exists()]
        missing = [f for f in input_files if not Path(f).exists()]
        if missing:
            logger.warning(f"以下文件不存在: {', '.join(str(f) for f in missing)}")

    # 按日期过滤文件
    if start_date or end_date:
        filtered_files = []
        for file_path in files:
            # 从文件名提取日期 (假设格式为 panews_flash_YYYYMMDD.csv)
            try:
                date_str = file_path.stem.split('_')[-1]  # 获取YYYYMMDD部分
                file_date = datetime.strptime(date_str, '%Y%m%d').date()

                if start_date:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
                    if file_date < start_dt:
                        continue

                if end_date:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
                    if file_date > end_dt:
                        continue

                filtered_files.append(file_path)

            except (ValueError, IndexError) as e:
                logger.warning(f"无法从文件名 {file_path.name} 解析日期: {e}")
                # 如果无法解析日期，仍然包含该文件
                filtered_files.append(file_path)

        files = filtered_files

    if sort_files and files:
        files.sort()  # 按完整路径字母顺序排序

    return files

def main():
    """主函数，解析参数并协调文件处理。"""
    parser = argparse.ArgumentParser(
        description="将新闻数据转换为Nautilus CustomData格式并写入catalog。",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # 输入文件选项 - 互斥组，默认使用filter_news目录
    input_group = parser.add_mutually_exclusive_group(required=False)
    input_group.add_argument('--input-dir', type=Path,
                           default=Path('process/filter_news'),
                           help='包含输入CSV文件的目录 (默认: process/filter_news)')
    input_group.add_argument('--input-file', type=Path, help='单个输入CSV文件的路径')
    input_group.add_argument('--input-files', type=Path, nargs='+', help='输入CSV文件路径列表')

    # Catalog和处理选项
    parser.add_argument('--catalog-path', type=Path, required=True,
                        help='Nautilus数据catalog目录的路径')
    parser.add_argument('--file-pattern', type=str, default="*_filtered.csv",
                        help='在--input-dir中匹配文件的Glob模式 (默认: *_filtered.csv)')
    parser.add_argument('--sort-files', action=argparse.BooleanOptionalAction, default=True,
                        help='按字母顺序排序输入文件')
    parser.add_argument('--log-level', type=str, default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                        help='设置日志级别')

    # 日期过滤选项
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYY-MM-DD)')

    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(args.log_level.upper())


    # 设置catalog目录
    args.catalog_path.mkdir(parents=True, exist_ok=True)


    catalog = ParquetDataCatalog(str(args.catalog_path))  # Catalog需要字符串路径

    # 获取要处理的文件列表
    if args.input_file:
        files_to_process = [args.input_file]
    else:
        files_to_process = get_file_list(
            input_dir=args.input_dir,
            input_files=args.input_files,
            file_pattern=args.file_pattern,
            start_date=args.start_date,
            end_date=args.end_date,
            sort_files=args.sort_files
        )

    if not files_to_process:
        logger.error("没有找到有效的输入文件。退出。")
        return

    # 处理文件
    total_files = len(files_to_process)
    total_records = 0

    logger.info(f"开始处理 {total_files} 个文件...")
    start_time = time.time()

    for i, file_path in enumerate(files_to_process):
        logger.info(f"处理文件 {i+1}/{total_files}: {file_path.name}")
        records = process_csv_file(
            file_path=file_path,
            catalog=catalog
        )
        total_records += records

    elapsed_time = time.time() - start_time
    logger.info(f"处理完成。处理了 {total_files} 个文件，{total_records} 条记录，用时 {elapsed_time:.2f} 秒")

if __name__ == "__main__":
    main()