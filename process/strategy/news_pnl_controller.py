from typing import Dict
from news_prediction_strategy import NewsPnlTradingStrategyConfig, NewsPnlTradingStrategy
from nautilus_trader.trading.controller import Controller
from nautilus_trader.common.config import ActorConfig
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.data import DataType
from nautilus_trader.core.data import Data
from nautilus_trader.trading.trader import Trader
from news_data_type import NewsData


class NewsPnlControllerConfig(ActorConfig, frozen=True):
    # 模型路径
    tokenizer_path: str
    tfidf_path: str
    model_path: str

    # BTC交易工具
    btc_instrument_str: str = "BTCUSDT-PERP.BINANCE"

    # 风险管理参数
    stop_loss_pct: float = 0.02
    low_trail_stop_loss_pct: float = 0.5
    trail_stop_loss_pct: float = 0.3
    higher_trail_stop_loss_pct: float = 0.2
    low_trail_profit_threshold: float = 0.01
    first_trail_profit_threshold: float = 0.02
    second_trail_profit_threshold: float = 0.03

    # 交易金额
    max_trade_usd: float = 1000.0
    min_trade_usd: float = 100.0

class NewsPnlController(Controller):
    def __init__(
        self,
        trader: Trader,
        config: NewsPnlControllerConfig | None = None,
    ) -> None:
        super().__init__(trader=trader, config=config)
        # 策略管理
        self.active_strategies: Dict[str, Strategy] = {}

    def on_start(self) -> None:
        """Controller启动时初始化"""
        self.log.info("🚀 新闻PnL控制器启动")
        # 订阅新闻数据
        self.subscribe_data(DataType(NewsData))
        self.log.info("✅ 已订阅新闻数据")

    def on_data(self, data) -> None:
        """处理新闻数据并预测PnL"""
        if isinstance(data, NewsData):
            self._process_news_data(data)

    def _process_news_data(self, news_data: NewsData) -> None:
        """处理新闻数据并执行策略"""
        news_instrument_id = news_data.instrument_id.value

        # 检查是否已有该工具的活跃策略
        if news_instrument_id in self.active_strategies:
            strategy = self.active_strategies[news_instrument_id]
            if isinstance(strategy, NewsPnlTradingStrategy):
                # 向现有策略发送新闻信号
                strategy.on_news_signal(news_data)
                self.log.info(f"发送新闻信号到现有策略: {strategy.id}")
        else:
            # 创建新的策略实例
            try:
                strategy = self._create_strategy_for_instrument(news_data)
                if strategy:
                    # 使用Controller的标准方法添加策略
                    self.create_strategy(strategy, start=True)
                    self.active_strategies[news_instrument_id] = strategy
                    self.log.info(f"创建并启动新策略: {strategy.id}")

                    # 发送初始新闻信号
                    strategy.on_news_signal(news_data)
            except Exception as e:
                self.log.error(f"创建策略失败 {news_instrument_id}: {e}")

        # 清理无仓位的策略
        self._cleanup_inactive_strategies()

    def _create_strategy_for_instrument(self, news_data: NewsData) -> NewsPnlTradingStrategy:
        """为特定工具创建策略实例"""
        news_instrument_id = news_data.instrument_id.value

        strategy_config = NewsPnlTradingStrategyConfig(
            tokenizer_path=self.config.tokenizer_path,
            tfidf_path=self.config.tfidf_path,
            model_path=self.config.model_path,
            instrument_str=news_instrument_id,
            btc_instrument_str=self.config.btc_instrument_str,
            bar_str=f"{news_instrument_id}-5-MINUTE-LAST-EXTERNAL",
            btc_bar_str=f"{self.config.btc_instrument_str}-5-MINUTE-LAST-EXTERNAL",
            stop_loss_pct=self.config.stop_loss_pct,
            low_trail_stop_loss_pct=self.config.low_trail_stop_loss_pct,
            trail_stop_loss_pct=self.config.trail_stop_loss_pct,
            higher_trail_stop_loss_pct=self.config.higher_trail_stop_loss_pct,
            low_trail_profit_threshold=self.config.low_trail_profit_threshold,
            first_trail_profit_threshold=self.config.first_trail_profit_threshold,
            second_trail_profit_threshold=self.config.second_trail_profit_threshold,
            max_trade_usd=self.config.max_trade_usd,
            min_trade_usd=self.config.min_trade_usd,
            strategy_id=f"NewsPnlStrategy-{news_instrument_id}",
            manage_gtd_expiry=True,
        )

        return NewsPnlTradingStrategy(config=strategy_config, start_news=news_data)

    def _cleanup_inactive_strategies(self) -> None:
        """清理没有活跃仓位的策略"""
        strategies_to_remove = []

        for instrument_id, strategy in self.active_strategies.items():
            try:
                # 检查策略是否有开放仓位
                open_positions = self.cache.positions_open(strategy_id=strategy.id)
                if len(open_positions) == 0:
                    self.log.info(f"策略 {strategy.id} 无活跃仓位，准备移除")
                    strategies_to_remove.append(instrument_id)
            except Exception as e:
                self.log.error(f"检查策略仓位时出错 {strategy.id}: {e}")
                strategies_to_remove.append(instrument_id)

        # 安全地移除策略
        for instrument_id in strategies_to_remove:
            strategy = self.active_strategies[instrument_id]
            try:
                # 使用Controller的标准方法移除策略
                self.remove_strategy(strategy)
                del self.active_strategies[instrument_id]
                self.log.info(f"成功移除策略: {strategy.id}")
            except Exception as e:
                self.log.error(f"移除策略失败 {strategy.id}: {e}")
                # 即使移除失败，也从字典中删除引用
                del self.active_strategies[instrument_id]

    def on_stop(self) -> None:
        """控制器停止时清理所有策略"""
        self.log.info("停止新闻PnL控制器，清理所有策略...")

        # 使用Controller标准方法停止和移除所有策略
        for instrument_id, strategy in list(self.active_strategies.items()):
            try:
                self.remove_strategy(strategy)
                self.log.info(f"成功移除策略: {strategy.id}")
            except Exception as e:
                self.log.error(f"移除策略失败 {strategy.id}: {e}")

        self.active_strategies.clear()
        self.log.info("所有策略已清理完成")

    # 策略管理的公共接口方法
    def get_active_strategies(self) -> Dict[str, Strategy]:
        """获取所有活跃策略"""
        return self.active_strategies.copy()

    def get_strategy_count(self) -> int:
        """获取活跃策略数量"""
        return len(self.active_strategies)

    def get_strategy_for_instrument(self, instrument_id: str) -> Strategy:
        """获取特定工具的策略"""
        return self.active_strategies.get(instrument_id)

    def force_remove_strategy(self, instrument_id: str) -> bool:
        """强制移除特定工具的策略"""
        if instrument_id in self.active_strategies:
            strategy = self.active_strategies[instrument_id]
            try:
                if hasattr(strategy, 'stop'):
                    strategy.stop()
                del self.active_strategies[instrument_id]
                self.log.info(f"强制移除策略成功: {strategy.id}")
                return True
            except Exception as e:
                self.log.error(f"强制移除策略失败 {strategy.id}: {e}")
                # 即使移除失败，也从字典中删除引用
                del self.active_strategies[instrument_id]
                return False
        return False

    def get_strategies_status(self) -> Dict[str, Dict]:
        """获取所有策略的状态信息"""
        status = {}
        for instrument_id, strategy in self.active_strategies.items():
            try:
                open_positions = self.cache.positions_open(strategy_id=strategy.id)
                status[instrument_id] = {
                    "strategy_id": str(strategy.id),
                    "state": str(strategy.state),
                    "open_positions": len(open_positions),
                    "is_running": strategy.is_running,
                }
            except Exception as e:
                status[instrument_id] = {
                    "strategy_id": str(strategy.id),
                    "state": "ERROR",
                    "error": str(e),
                }
        return status
        