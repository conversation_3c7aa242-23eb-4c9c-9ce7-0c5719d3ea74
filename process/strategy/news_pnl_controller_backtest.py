#!/usr/bin/env python3
"""
新闻PnL控制器回测脚本

测试 NewsPnlController 的功能，包括：
1. 动态策略创建和管理
2. 新闻数据处理
3. 多策略协调
4. 风险管理
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timezone, timedelta
from decimal import Decimal
import sys

# 添加路径
sys.path.append('/root/news_train')
sys.path.append('/root/news_train/process/strategy')

# Nautilus Trader imports
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.config import BacktestRunConfig, BacktestEngineConfig, BacktestVenueConfig, BacktestDataConfig
from nautilus_trader.config import LoggingConfig
from nautilus_trader.live.config import ImportableControllerConfig
from nautilus_trader.model.currencies import USDT
from nautilus_trader.model.enums import AccountType, OmsType
from nautilus_trader.model.identifiers import InstrumentId, Venue
from nautilus_trader.model.objects import Money
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.serialization.arrow.serializer import register_arrow
from nautilus_trader.backtest.models import FillModel
from nautilus_trader.model.data import Bar
from nautilus_trader.core.datetime import dt_to_unix_nanos

# 策略和数据类型导入
from news_pnl_controller import NewsPnlController, NewsPnlControllerConfig
from news_data_type import NewsData

# 注册自定义数据类型
register_arrow(
    NewsData,
    NewsData.schema(),
    NewsData.to_catalog,
    NewsData.from_catalog,
)


def get_supported_instruments() -> list[str]:
    """获取支持的交易工具列表"""
    return [
        "1000PEPEUSDT-PERP.BINANCE",
        "ETHUSDT-PERP.BINANCE",
        "SOLUSDT-PERP.BINANCE",
        "BTCUSDT-PERP.BINANCE",
        "DOGEUSDT-PERP.BINANCE"
    ]


def run_news_pnl_controller_backtest() -> None:
    """
    运行新闻PnL控制器回测
    """
    print("🚀 开始新闻PnL控制器回测...")
    print("📈 测试多策略动态管理")
    print("🤖 使用最优模型: 准确率 69.11%")
    
    # 路径配置 - 使用绝对路径
    catalog_path = "/root/nautilus_trader-develop/examples/catalog"

    # 使用最优模型路径 - 绝对路径
    tokenizer_path = "/root/news_train/models/baseline_2k.model"
    tfidf_path = "/root/news_train/models/optimal/tfidf_vectorizer.pkl"
    model_path = "/root/news_train/models/optimal/rf_model.pkl"

    # 获取支持的交易工具
    supported_instruments = get_supported_instruments()
    print(f"📊 支持的交易工具: {len(supported_instruments)} 个")
    for instrument in supported_instruments:
        print(f"   - {instrument}")
    
    # 检查必要文件
    required_files = [tokenizer_path, tfidf_path, model_path]
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请确保模型文件存在")
        return False
    
    print("✅ 所有必要文件检查通过")
    

    # 创建填充模型
    fill_model = FillModel(
        prob_fill_on_limit=0.7,    # 限价单成交概率
        prob_fill_on_stop=0.95,    # 止损单成交概率
        prob_slippage=0.5,         # 滑点概率
        random_seed=42,            # 随机种子，确保结果可重现
    )

    # 数据配置 - 加载真实的K线数据和新闻数据
    data_configs = [
        # 新闻数据配置
        BacktestDataConfig(
            catalog_path=str(catalog_path),
            data_cls=NewsData.fully_qualified_name(),
            client_id="PANEWS",
            start_time=datetime(2025, 1, 1),
            end_time=datetime(2025, 1, 31),
        ),
    ]

    # 为每个支持的交易工具添加K线数据配置
    for instrument_id in supported_instruments:
        data_configs.append(
            BacktestDataConfig(
                catalog_path=str(catalog_path),
                data_cls=Bar.fully_qualified_name(),
                instrument_id=InstrumentId.from_str(instrument_id),
                start_time=datetime(2025, 1, 1),
                end_time=datetime(2025, 1, 31),
                client_id="BINANCE",
            )
        )

    print(f"📊 配置了 {len(data_configs)} 个数据源:")
    print(f"   - 1个新闻数据源")
    print(f"   - {len(supported_instruments)}个K线数据源")
    
    # 场所配置 - 不直接使用FillModel对象，而是使用配置
    venues_configs = [
        BacktestVenueConfig(
            name="BINANCE",
            oms_type="NETTING",
            account_type="MARGIN",
            base_currency="USDT",
            starting_balances=["100000 USDT"],
            # fill_model 会在引擎中自动创建
        )
    ]
    
    # 控制器配置
    controller_config = ImportableControllerConfig(
        controller_path="news_pnl_controller:NewsPnlController",
        config_path="news_pnl_controller:NewsPnlControllerConfig",
        config={
            "tokenizer_path": tokenizer_path,
            "tfidf_path": tfidf_path,
            "model_path": model_path,
            "btc_instrument_str": "BTCUSDT-PERP.BINANCE",
            "stop_loss_pct": 0.02,
            "low_trail_stop_loss_pct": 0.5,
            "trail_stop_loss_pct": 0.3,
            "higher_trail_stop_loss_pct": 0.2,
            "low_trail_profit_threshold": 0.01,
            "first_trail_profit_threshold": 0.02,
            "second_trail_profit_threshold": 0.03,
            "max_trade_usd": 1000.0,
            "min_trade_usd": 100.0,
        }
    )
    
    # 回测配置 - 增加时间范围
    backtest_configs = [
        BacktestRunConfig(
            engine=BacktestEngineConfig(
                controller=controller_config,
                logging=LoggingConfig(log_level="INFO"),
            ),
            venues=venues_configs,
            data=data_configs,
            start="2025-01-01T00:00:00Z",
            end="2025-01-31T23:59:59Z",
        )
    ]
    
    print("📁 创建回测节点...")
    # 创建回测节点
    node = BacktestNode(backtest_configs)
    
    try:
        print("⏳ 正在运行回测...")
        # 运行回测
        node.run()
        
        print("✅ 回测完成!")
        
        # 获取回测结果
        engine = node.get_engine(backtest_configs[0].id)
        
        if engine:
            print("\n📊 回测结果分析:")

            # 生成详细报告
            with pd.option_context(
                "display.max_rows", 100,
                "display.max_columns", None,
                "display.width", 300,
            ):
                print("\n📋 订单成交报告:")
                order_fills_report = engine.trader.generate_order_fills_report()
                if not order_fills_report.empty:
                    print(order_fills_report)
                else:
                    print("   无订单成交记录")

                print("\n📊 持仓报告:")
                positions_report = engine.trader.generate_positions_report()
                if not positions_report.empty:
                    print(positions_report)
                else:
                    print("   无持仓记录")

            # 账户信息
            accounts = engine.cache.accounts()
            if accounts:
                account = accounts[0]  # 获取第一个账户
                print(f"\n💰 最终账户余额: {account.balance_total()}")
                # 获取未实现PnL（如果有的话）
                try:
                    unrealized_pnl = account.unrealized_pnl() if hasattr(account, 'unrealized_pnl') else "N/A"
                    print(f"💵 未实现PnL: {unrealized_pnl}")
                except:
                    print(f"💵 未实现PnL: N/A")
            else:
                print("\n💰 未找到账户信息")

            # 统计信息
            orders = engine.cache.orders()
            positions = engine.cache.positions()

            print(f"\n📈 交易统计:")
            print(f"📋 总订单数: {len(orders)}")
            print(f"📊 总仓位数: {len(positions)}")

            if orders:
                filled_orders = [o for o in orders if o.is_filled]
                print(f"✅ 成交订单数: {len(filled_orders)}")

                if filled_orders:
                    buy_orders = [o for o in filled_orders if o.side.name == 'BUY']
                    sell_orders = [o for o in filled_orders if o.side.name == 'SELL']
                    print(f"📈 买单: {len(buy_orders)}")
                    print(f"📉 卖单: {len(sell_orders)}")

                    # 按交易工具分组统计
                    instrument_stats = {}
                    for order in filled_orders:
                        instrument = str(order.instrument_id)
                        if instrument not in instrument_stats:
                            instrument_stats[instrument] = {"buy": 0, "sell": 0}
                        if order.side.name == 'BUY':
                            instrument_stats[instrument]["buy"] += 1
                        else:
                            instrument_stats[instrument]["sell"] += 1

                    print(f"\n📊 各交易工具统计:")
                    for instrument, stats in instrument_stats.items():
                        symbol = instrument.split('-')[0]
                        print(f"   {symbol}: 买单{stats['buy']}个, 卖单{stats['sell']}个")

            if positions:
                closed_positions = [p for p in positions if p.is_closed]
                print(f"\n💼 仓位统计:")
                print(f"🔒 已关闭仓位: {len(closed_positions)}")

                if closed_positions:
                    total_pnl = sum(p.realized_pnl.as_double() for p in closed_positions)
                    print(f"💵 已实现PnL: {total_pnl:.2f} USDT")

                    profitable_positions = [p for p in closed_positions if p.realized_pnl.as_double() > 0]
                    win_rate = len(profitable_positions) / len(closed_positions) * 100
                    print(f"🎯 胜率: {win_rate:.1f}%")

                    # 按交易工具分组PnL统计
                    instrument_pnl = {}
                    for position in closed_positions:
                        instrument = str(position.instrument_id)
                        if instrument not in instrument_pnl:
                            instrument_pnl[instrument] = []
                        instrument_pnl[instrument].append(position.realized_pnl.as_double())

                    print(f"\n💰 各交易工具PnL:")
                    for instrument, pnls in instrument_pnl.items():
                        symbol = instrument.split('-')[0]
                        total_pnl = sum(pnls)
                        avg_pnl = total_pnl / len(pnls)
                        print(f"   {symbol}: 总PnL {total_pnl:.2f} USDT, 平均 {avg_pnl:.2f} USDT")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        node.dispose()


def main():
    """主函数"""
    print("=" * 80)
    print("🎯 新闻PnL控制器回测系统")
    print("=" * 80)
    print("📊 使用最优模型配置:")
    print("   - 模型: RandomForest + TF-IDF")
    print("   - 准确率: 69.11%")
    print("   - 特征维度: 306")
    print("   - 支持多策略动态管理")
    print("📈 支持的交易工具:")
    for instrument in get_supported_instruments():
        symbol = instrument.split('-')[0]
        print(f"   - {symbol}")
    print("=" * 80)

    # 运行回测
    success = run_news_pnl_controller_backtest()

    if success:
        print("\n🎉 新闻PnL控制器回测完成!")
        print("💡 回测使用了真实的K线数据和最优训练模型")
        print("💡 控制器支持多策略动态创建和管理")
        print("💡 基于新闻事件自动创建相应的交易策略")
    else:
        print("\n💥 新闻PnL控制器回测失败!")
        print("💡 请检查数据文件和模型文件是否存在")

    print("=" * 80)


if __name__ == "__main__":
    main()
