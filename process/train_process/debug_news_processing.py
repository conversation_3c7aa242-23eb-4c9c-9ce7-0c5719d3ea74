#!/usr/bin/env python3
"""
调试新闻处理脚本
"""

import pandas as pd
import sys
sys.path.append('.')
from create_news_training_samples import (
    parse_matched_symbols, 
    is_excluded_symbol, 
    convert_timestamp_ns_to_datetime,
    get_available_symbols,
    load_symbol_labeled_data
)

def debug_single_news_file(file_path, max_rows=10):
    """调试单个新闻文件的处理过程"""
    print(f"=== 调试文件: {file_path} ===")

    # 读取新闻文件
    try:
        news_df = pd.read_csv(file_path)
        print(f"✅ 成功读取文件，共 {len(news_df)} 条新闻")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return

    # 获取可用symbols
    available_symbols = get_available_symbols()
    print(f"📋 可用symbols数量: {len(available_symbols)}")
    print(f"🔗 前10个symbols: {available_symbols[:10]}")

    # 统计信息
    total_processed = 0
    total_skipped = 0
    total_samples = 0
    skip_reasons = {}

    # 处理所有新闻，但只详细显示前几条
    for i, (_, news_row) in enumerate(news_df.iterrows()):
        show_details = i < max_rows

        if show_details:
            print(f"\n--- 处理第 {i+1} 条新闻 ---")

        # 提取基本信息
        title = news_row.get('title', '')
        matched_symbols = news_row.get('matched_symbols', '')
        timestamp_ns = news_row.get('timestamp_ns', '')

        if show_details:
            print(f"📰 标题: {title[:50]}...")
            print(f"🔗 原始matched_symbols: {matched_symbols}")
            print(f"⏰ timestamp_ns: {timestamp_ns}")

        # 解析symbols
        symbols = parse_matched_symbols(matched_symbols)
        if show_details:
            print(f"🎯 解析后的symbols: {symbols}")

        if not symbols:
            if show_details:
                print("⚠️  没有解析到symbols，跳过")
            total_skipped += 1
            skip_reasons['no_symbols'] = skip_reasons.get('no_symbols', 0) + 1
            continue

        # 检查时间戳转换
        utc_time = convert_timestamp_ns_to_datetime(timestamp_ns)
        if show_details:
            print(f"🕐 转换后的UTC时间: {utc_time}")

        if utc_time is None:
            if show_details:
                print("⚠️  时间戳转换失败，跳过")
            total_skipped += 1
            skip_reasons['timestamp_error'] = skip_reasons.get('timestamp_error', 0) + 1
            continue

        # 检查每个symbol
        for symbol in symbols:
            if show_details:
                print(f"\n  🔍 检查symbol: {symbol}")

            # 检查是否被排除
            if is_excluded_symbol(symbol):
                if show_details:
                    print(f"    ❌ 被排除规则过滤")
                total_skipped += 1
                skip_reasons['excluded'] = skip_reasons.get('excluded', 0) + 1
                continue

            # 检查是否在可用列表中
            if symbol not in available_symbols:
                if show_details:
                    print(f"    ❌ 不在可用symbols列表中")
                total_skipped += 1
                skip_reasons['not_available'] = skip_reasons.get('not_available', 0) + 1
                continue

            # 尝试加载数据
            labeled_df = load_symbol_labeled_data(symbol)
            if labeled_df is None:
                if show_details:
                    print(f"    ❌ 无法加载标签数据")
                total_skipped += 1
                skip_reasons['load_error'] = skip_reasons.get('load_error', 0) + 1
                continue

            if show_details:
                print(f"    ✅ 成功加载标签数据，共 {len(labeled_df)} 行")
                print(f"    📊 时间范围: {labeled_df['timestamp'].min()} 到 {labeled_df['timestamp'].max()}")

            # 检查是否有匹配的时间点
            future_data = labeled_df[labeled_df['timestamp'] >= utc_time]
            if show_details:
                print(f"    🎯 找到 {len(future_data)} 个未来时间点")

            if len(future_data) > 0:
                if show_details:
                    print(f"    ✅ 可以生成训练样本！")
                total_samples += 1
                total_processed += 1
            else:
                if show_details:
                    print(f"    ⚠️  没有找到未来时间点")
                total_skipped += 1
                skip_reasons['no_future_data'] = skip_reasons.get('no_future_data', 0) + 1

    # 打印统计信息
    print(f"\n=== 处理统计 ===")
    print(f"📊 总新闻数: {len(news_df)}")
    print(f"✅ 可生成样本数: {total_samples}")
    print(f"⚠️  跳过数: {total_skipped}")
    print(f"📈 成功率: {total_samples/(total_samples+total_skipped)*100:.1f}%")
    print(f"\n跳过原因统计:")
    for reason, count in skip_reasons.items():
        print(f"  {reason}: {count}")

    return total_samples

def main():
    """主函数"""
    # 调试第一个新闻文件
    file_path = "../filter_news/panews_flash_20250101_filtered.csv"
    samples = debug_single_news_file(file_path, max_rows=5)

    if samples == 0:
        print(f"\n❌ 没有生成任何样本，让我们检查更多文件...")
        # 检查更多文件
        import glob
        files = glob.glob("../filter_news/panews_flash_*_filtered.csv")[:3]
        for file_path in files[1:]:
            print(f"\n" + "="*50)
            samples = debug_single_news_file(file_path, max_rows=3)
            if samples > 0:
                print(f"✅ 在文件 {file_path} 中找到了 {samples} 个样本！")
                break

if __name__ == "__main__":
    main()
